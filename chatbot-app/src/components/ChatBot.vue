<template>
  <div class="chatbot-container">
    <!-- Header -->
    <div class="chatbot-header">
      <h2>AI Chatbot</h2>
      <div class="header-controls">
        <span class="session-id">Session: {{ sessionId }}</span>
        <button @click="resetChat" class="reset-btn" :disabled="loading">
          Reset Chat
        </button>
        <button @click="logout" class="logout-btn">Logout</button>
      </div>
    </div>

    <!-- Authentication Status -->
    <div v-if="!isAuthenticated" class="auth-status">
      <p>Authenticating...</p>
      <button @click="authenticate" :disabled="authenticating">
        {{ authenticating ? "Authenticating..." : "Retry Authentication" }}
      </button>
    </div>

    <!-- Chat Messages -->
    <div v-else class="chat-messages" ref="messagesContainer">
      <div
        v-for="(message, index) in messages"
        :key="index"
        :class="['message', message.type]"
      >
        <div class="message-content">
          <div class="message-text">{{ message.text }}</div>
          <div class="message-time">{{ message.time }}</div>
        </div>
      </div>
      <div v-if="loading" class="message bot">
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div v-if="isAuthenticated" class="chat-input">
      <!-- Mode Selection -->
      <div class="mode-selection">
        <label>
          <input type="radio" v-model="mode" value="chat" />
          Chat
        </label>
        <label>
          <input type="radio" v-model="mode" value="query" />
          Query
        </label>
      </div>

      <!-- File Upload -->
      <div class="file-upload">
        <input
          type="file"
          ref="fileInput"
          @change="handleFileSelect"
          multiple
          accept="image/*,.pdf,.txt,.doc,.docx"
          style="display: none"
        />
        <button @click="$refs.fileInput.click()" class="file-btn">
          📎 Attach Files
        </button>
        <span v-if="selectedFiles.length > 0" class="file-count">
          {{ selectedFiles.length }} file(s) selected
        </span>
      </div>

      <!-- Selected Files -->
      <div v-if="selectedFiles.length > 0" class="selected-files">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="file-item"
        >
          <span>{{ file.name }}</span>
          <button @click="removeFile(index)" class="remove-file">×</button>
        </div>
      </div>

      <!-- Message Input -->
      <div class="input-row">
        <textarea
          v-model="currentMessage"
          @keydown.enter.prevent="sendMessage"
          placeholder="Type your message..."
          :disabled="loading"
          rows="3"
        ></textarea>
        <button
          @click="sendMessage"
          :disabled="loading || !currentMessage.trim()"
        >
          Send
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import authService from "../services/auth.js";
import chatService from "../services/chat.js";

export default {
  name: "ChatBot",
  data() {
    return {
      isAuthenticated: false,
      authenticating: false,
      messages: [],
      currentMessage: "",
      loading: false,
      mode: "chat",
      selectedFiles: [],
      sessionId: "",
    };
  },
  async mounted() {
    this.sessionId = chatService.getSessionId();
    await this.authenticate();
  },
  methods: {
    async authenticate() {
      this.authenticating = true;
      try {
        const success = await authService.authenticate();
        this.isAuthenticated = success;
        if (success) {
          this.addMessage("System", "Connected successfully!", "system");
        } else {
          this.addMessage(
            "System",
            "Authentication failed. Please try again.",
            "error"
          );
        }
      } catch (error) {
        this.addMessage(
          "System",
          "Authentication error: " + error.message,
          "error"
        );
      } finally {
        this.authenticating = false;
      }
    },
    async sendMessage() {
      if (!this.currentMessage.trim() || this.loading) return;

      const message = this.currentMessage.trim();
      this.addMessage("You", message, "user");
      this.currentMessage = "";
      this.loading = true;

      try {
        // Prepare attachments if any files are selected
        const attachments = await chatService.prepareAttachments(
          this.selectedFiles
        );

        // Send message
        const response = await chatService.sendMessage(
          message,
          this.mode,
          attachments
        );

        // Add bot response
        this.addMessage(
          "Bot",
          response.message || "No response received",
          "bot"
        );

        // Clear selected files after sending
        this.selectedFiles = [];
      } catch (error) {
        this.addMessage("System", "Error: " + error.message, "error");
      } finally {
        this.loading = false;
        this.scrollToBottom();
      }
    },
    async resetChat() {
      try {
        this.loading = true;
        await chatService.resetChat();
        this.messages = [];
        this.selectedFiles = [];
        this.addMessage("System", "Chat has been reset", "system");
      } catch (error) {
        this.addMessage(
          "System",
          "Error resetting chat: " + error.message,
          "error"
        );
      } finally {
        this.loading = false;
      }
    },
    logout() {
      authService.logout();
      this.isAuthenticated = false;
      this.messages = [];
      this.selectedFiles = [];
    },
    addMessage(sender, text, type) {
      this.messages.push({
        sender,
        text,
        type,
        time: new Date().toLocaleTimeString(),
      });
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    handleFileSelect(event) {
      const files = Array.from(event.target.files);
      this.selectedFiles = [...this.selectedFiles, ...files];
    },
    removeFile(index) {
      this.selectedFiles.splice(index, 1);
    },
  },
};
</script>

<style scoped>
.chatbot-container {
  max-width: 800px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.chatbot-header {
  background: #007bff;
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatbot-header h2 {
  margin: 0;
}

.header-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.session-id {
  font-size: 0.8rem;
  opacity: 0.8;
}

.reset-btn,
.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.reset-btn:hover,
.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.reset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.auth-status {
  padding: 2rem;
  text-align: center;
  background: #f8f9fa;
}

.auth-status button {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #f8f9fa;
}

.message {
  margin-bottom: 1rem;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.bot,
.message.system,
.message.error {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  position: relative;
}

.message.user .message-content {
  background: #007bff;
  color: white;
}

.message.bot .message-content {
  background: white;
  border: 1px solid #ddd;
}

.message.system .message-content {
  background: #28a745;
  color: white;
}

.message.error .message-content {
  background: #dc3545;
  color: white;
}

.message-text {
  word-wrap: break-word;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.chat-input {
  border-top: 1px solid #ddd;
  padding: 1rem;
  background: white;
}

.mode-selection {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.mode-selection label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;
}

.file-upload {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.file-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.file-count {
  font-size: 0.8rem;
  color: #666;
}

.selected-files {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.remove-file {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.input-row {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.input-row textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.75rem;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.input-row button {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  height: fit-content;
}

.input-row button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
</style>
