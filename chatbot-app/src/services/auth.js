import axios from "axios";

const API_KEY = "NXCSDM4-AJ44B91-PBRPS0C-0PK6SGX";
const BASE_URL = "http://localhost:3001/api/v1";

class AuthService {
  constructor() {
    this.token = localStorage.getItem("authToken");
    this.setupAxiosInterceptors();
  }

  setupAxiosInterceptors() {
    // Add request interceptor to include auth token
    axios.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor to handle auth errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logout();
        }
        return Promise.reject(error);
      }
    );
  }

  async authenticate() {
    try {
      const response = await axios.get(`${BASE_URL}/auth`, {
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${API_KEY}`,
        },
      });

      // API key'in kendisi token olarak kullanılıyor
      this.token = API_KEY;
      localStorage.setItem("authToken", this.token);
      return true;
    } catch (error) {
      console.error("Authentication failed:", error);
      return false;
    }
  }

  logout() {
    this.token = null;
    localStorage.removeItem("authToken");
  }

  isAuthenticated() {
    return !!this.token;
  }

  getToken() {
    return this.token;
  }
}

export default new AuthService();
