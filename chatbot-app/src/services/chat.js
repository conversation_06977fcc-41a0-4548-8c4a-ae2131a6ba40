import axios from 'axios'

const BASE_URL = 'http://localhost:3001/api/v1'
const CHAT_ENDPOINT = `${BASE_URL}/workspace/mywork/chat`

class ChatService {
  constructor() {
    this.sessionId = this.generateSessionId()
  }

  generateSessionId() {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  async sendMessage(message, mode = 'chat', attachments = [], reset = false) {
    try {
      const payload = {
        message,
        mode,
        sessionId: this.sessionId,
        attachments,
        reset
      }

      const response = await axios.post(CHAT_ENDPOINT, payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return response.data
    } catch (error) {
      console.error('Chat request failed:', error)
      throw error
    }
  }

  async sendQuery(message, attachments = []) {
    return this.sendMessage(message, 'query', attachments)
  }

  async sendChat(message, attachments = []) {
    return this.sendMessage(message, 'chat', attachments)
  }

  resetSession() {
    this.sessionId = this.generateSessionId()
  }

  async resetChat() {
    return this.sendMessage('', 'chat', [], true)
  }

  getSessionId() {
    return this.sessionId
  }

  // Helper method to convert file to base64
  async fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  // Helper method to prepare file attachments
  async prepareAttachments(files) {
    const attachments = []
    
    for (const file of files) {
      try {
        const contentString = await this.fileToBase64(file)
        attachments.push({
          name: file.name,
          mime: file.type,
          contentString
        })
      } catch (error) {
        console.error('Error processing file:', file.name, error)
      }
    }
    
    return attachments
  }
}

export default new ChatService()
