bana aşağ<PERSON><PERSON><PERSON> bilgiler kull<PERSON>rak vuejs de bir chat boot yap, 
yarn kullan 
api key NXCSDM4-AJ44B91-PBRPS0C-0PK6SGX
once auth olmanlazım curl -X 'GET' \
  'http://localhost:3001/api/v1/auth' \
  -H 'accept: application/json'  
sonrasında tüm sorgular BearerAuth ile yapmanlazım



api BASE_URL  http://localhost:3001/api/v1
chat  {{BASE_URL}}/workspace/mywork/chat
 
post method  BearerAuth ile gonderecen
ornek json
{
  "message": "merhaba",
  "mode": "query | chat",
  "sessionId": "identifier-to-partition-chats-by-external-id",
  "attachments": [
    {
      "name": "image.png",
      "mime": "image/png",
      "contentString": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
    }
  ],
  "reset": false
}

 